function [population, objectives, convergence_history] = RunNSGAII(problem, params)
% RunNSGAII - 运行NSGA-II算法用于多目标优化
%
% 输入:
%   problem - 包含问题信息的结构体
%   params  - 算法参数
%
% 输出:
%   population - 最终种群
%   objectives - 最终的非支配解集目标函数值
%   convergence_history - 收敛历史记录，包含每代的最小总质量
%
% 参考文献:
% <PERSON><PERSON>, <PERSON><PERSON>, S. <PERSON>, and <PERSON><PERSON>, A fast and elitist
% multiobjective genetic algorithm: NSGA-II, IEEE Transactions on
% Evolutionary Computation, 2002, 6(2): 182-197.

%% 初始化参数
nVar = problem.nVar;
varSize = problem.varSize;
varMin = problem.varMin;
varMax = problem.varMax;
nPop = params.nPop;
maxIt = params.maxIt;
nObj = problem.nObj;
pCrossover = params.pCrossover;
pMutation = params.pMutation;

% 确保有评价次数计数器
if ~isfield(problem, 'FE')
    problem.FE = 0;
end

%% 初始化种群
empty_individual.Position = [];
empty_individual.Cost = [];
empty_individual.Rank = [];
empty_individual.DominationSet = [];
empty_individual.DominatedCount = [];
empty_individual.CrowdingDistance = [];

pop = repmat(empty_individual, nPop, 1);

for i = 1:nPop
    % 生成随机解
    pop(i).Position = unifrnd(varMin, varMax, varSize);
    
    % 对离散变量特殊处理
    if isfield(problem, 'discreteVars')
        for j = 1:length(problem.discreteVars)
            idx = problem.discreteVars(j).idx;
            if problem.discreteVars(j).isInteger
                pop(i).Position(idx) = round(pop(i).Position(idx));
            else
                % 找到最接近的离散值
                values = problem.discreteVars(j).values;
                [~, closest_idx] = min(abs(pop(i).Position(idx) - values));
                pop(i).Position(idx) = values(closest_idx);
            end
        end
    end
    
    % 评估目标函数
    pop(i).Cost = problem.costFunction(pop(i).Position);
    problem.FE = problem.FE + 1;
end

% 非支配排序
[pop, F] = NonDominatedSorting(pop);

% 计算拥挤度距离
pop = CalcCrowdingDistance(pop, F);

% 排序种群
pop = SortPopulation(pop);

% 初始化收敛历史记录
convergence_history = struct();
convergence_history.iteration = zeros(maxIt + 1, 1);
convergence_history.min_mass = zeros(maxIt + 1, 1);
convergence_history.algorithm_name = 'NSGA-II';

% 记录初始代的最小总质量（纯齿轮质量，与最终结果表格计算方法一致）
current_costs = vertcat(pop.Cost);
valid_costs = current_costs(~any(isnan(current_costs) | isinf(current_costs), 2), :);
if ~isempty(valid_costs)
    % 找到目标函数值最小的解
    [~, min_idx] = min(valid_costs(:, 1));
    best_individual_idx = find(~any(isnan(current_costs) | isinf(current_costs), 2));
    if ~isempty(best_individual_idx) && min_idx <= length(best_individual_idx)
        best_idx = best_individual_idx(min_idx);
        % 使用与最终结果表格完全相同的方法计算纯齿轮质量
        pure_mass = calculateRealGearMass(pop(best_idx).Position, problem);
        convergence_history.min_mass(1) = pure_mass;
    else
        convergence_history.min_mass(1) = inf;
    end
else
    convergence_history.min_mass(1) = inf;
end
convergence_history.iteration(1) = 0;

%% 优化主循环
for it = 1:maxIt
    % 交叉
    popc = repmat(empty_individual, nPop, 1);
    for i = 1:nPop/2
        % 锦标赛选择
        p1 = TournamentSelection(pop);
        p2 = TournamentSelection(pop);
        
        % 交叉
        [popc(2*i-1).Position, popc(2*i).Position] = Crossover(p1.Position, p2.Position, pCrossover, varMin, varMax);
        
        % 变异
        popc(2*i-1).Position = Mutate(popc(2*i-1).Position, pMutation, varMin, varMax);
        popc(2*i).Position = Mutate(popc(2*i).Position, pMutation, varMin, varMax);
        
        % 对离散变量特殊处理
        if isfield(problem, 'discreteVars')
            for k = [2*i-1, 2*i]
                for j = 1:length(problem.discreteVars)
                    idx = problem.discreteVars(j).idx;
                    if problem.discreteVars(j).isInteger
                        popc(k).Position(idx) = round(popc(k).Position(idx));
                    else
                        % 找到最接近的离散值
                        values = problem.discreteVars(j).values;
                        [~, closest_idx] = min(abs(popc(k).Position(idx) - values));
                        popc(k).Position(idx) = values(closest_idx);
                    end
                end
            end
        end
        
        % 评估子代
        popc(2*i-1).Cost = problem.costFunction(popc(2*i-1).Position);
        popc(2*i).Cost = problem.costFunction(popc(2*i).Position);
        problem.FE = problem.FE + 2;
    end
    
    % 合并父代和子代
    pop = [pop; popc];
    
    % 非支配排序
    [pop, F] = NonDominatedSorting(pop);
    
    % 计算拥挤度距离
    pop = CalcCrowdingDistance(pop, F);
    
    % 精英选择
    pop = EliteSelection(pop, nPop);

    % 记录当前迭代的最小总质量（纯齿轮质量，与最终结果表格计算方法一致）
    current_costs = vertcat(pop.Cost);
    valid_costs = current_costs(~any(isnan(current_costs) | isinf(current_costs), 2), :);
    if ~isempty(valid_costs)
        % 找到目标函数值最小的解
        [~, min_idx] = min(valid_costs(:, 1));
        best_individual_idx = find(~any(isnan(current_costs) | isinf(current_costs), 2));
        if ~isempty(best_individual_idx) && min_idx <= length(best_individual_idx)
            best_idx = best_individual_idx(min_idx);
            % 使用与最终结果表格完全相同的方法计算纯齿轮质量
            pure_mass = calculateRealGearMass(pop(best_idx).Position, problem);
            convergence_history.min_mass(it + 1) = pure_mass;
        else
            convergence_history.min_mass(it + 1) = inf;
        end
    else
        convergence_history.min_mass(it + 1) = inf;
    end
    convergence_history.iteration(it + 1) = it;

    % 显示迭代信息
    if mod(it, 10) == 0 || it == maxIt
        % 计算非支配解数量
        n_nondom = numel(F{1});
        % 显示纯齿轮质量（与最终结果表格计算方法一致）
        display_mass = convergence_history.min_mass(it + 1);
        if display_mass == inf
            disp(['迭代 ' num2str(it) '/' num2str(maxIt) ', 非支配解数量 = ' num2str(n_nondom) ', 评价次数 = ' num2str(problem.FE) ', 最小总质量 = 无效']);
        else
            disp(['迭代 ' num2str(it) '/' num2str(maxIt) ', 非支配解数量 = ' num2str(n_nondom) ', 评价次数 = ' num2str(problem.FE) ', 最小总质量 = ' num2str(display_mass, '%.2f') ' kg']);
        end
    end
end

% 再次对最终种群进行非支配排序
[pop, F] = NonDominatedSorting(pop);

% 提取非支配解集
% 确保F{1}中的索引不超出pop的范围
valid_indices = F{1}(F{1} <= length(pop));
if isempty(valid_indices)
    % 如果没有有效的非支配解，使用整个种群
    nonDominated = pop;
else
    nonDominated = pop(valid_indices);
end

% 返回最终结果
n_pareto = numel(nonDominated);
population = zeros(n_pareto, nVar);
objectives = zeros(n_pareto, nObj);

for i = 1:n_pareto
    population(i, :) = nonDominated(i).Position;
    objectives(i, :) = nonDominated(i).Cost;
end

% 处理最大化目标
objectives(:, 2) = -objectives(:, 2);  % 取负值以便最大化弯曲安全系数
objectives(:, 3) = -objectives(:, 3);  % 取负值以便最大化接触安全系数

% 裁剪收敛历史记录到实际使用的长度
convergence_history.iteration = convergence_history.iteration(1:maxIt+1);
convergence_history.min_mass = convergence_history.min_mass(1:maxIt+1);

% 收敛历史记录保持原始目标函数值（包含惩罚项）
% 不进行任何转换，保持数据的真实性

% 不再输出算法完成信息，由主程序统一处理
end

function real_mass = calculateRealGearMass(x, problem)
% 计算真实齿轮质量（与最终结果表格计算方法完全一致）
% 使用与createParameterTable函数中相同的计算逻辑

persistent mass_cache;
if isempty(mass_cache)
    mass_cache = containers.Map();
end

try
    % 创建缓存键
    cache_key = sprintf('%.6f_', x);

    % 检查缓存
    if isKey(mass_cache, cache_key)
        real_mass = mass_cache(cache_key);
        return;
    end

    % 获取系统参数
    if ~isfield(problem, 'system_params') || ~isfield(problem, 'first_stage_params')
        real_mass = inf;
        return;
    end

    system_params = problem.system_params;
    first_stage_params = problem.first_stage_params;

    % 调用GearSystemCalculator计算完整的齿轮系统（与createParameterTable中的方法一致）
    [gear_system, ~, ~] = GearSystemCalculator(x, system_params);

    % 提取真实齿轮质量（与createParameterTable中第4465行的计算完全一致）
    if isfield(gear_system, 'M1') && isfield(gear_system, 'M2') && ...
       isfield(gear_system, 'Ms2') && isfield(gear_system, 'Mp2') && isfield(gear_system, 'Mr2') && ...
       isfield(gear_system, 'Ms3') && isfield(gear_system, 'Mp3') && isfield(gear_system, 'Mr3')

        % 计算纯齿轮质量（从各级质量相加）- 与OptimizationMain.m第4465行完全一致
        pure_mass = gear_system.M1 + gear_system.M2 + gear_system.Ms2 + ...
                   gear_system.Mp2 + gear_system.Mr2 + gear_system.Ms3 + ...
                   gear_system.Mp3 + gear_system.Mr3;

        real_mass = round(pure_mass, 2);  % 保留两位小数，与表格格式一致

        % 存入缓存
        mass_cache(cache_key) = real_mass;
    else
        real_mass = inf;
    end

catch ME
    % 如果计算失败，返回无效值
    real_mass = inf;
end

end



%% 辅助函数
function [pop, F] = NonDominatedSorting(pop)
% 非支配排序

    nPop = numel(pop);
    
    % 初始化
    for i = 1:nPop
        pop(i).DominationSet = [];
        pop(i).DominatedCount = 0;
    end
    
    F{1} = [];
    
    % 计算支配关系
    for i = 1:nPop
        for j = i+1:nPop
            p = pop(i);
            q = pop(j);
            
            % 检查目标函数值是否有效
            p_valid = ~any(isnan(p.Cost) | isinf(p.Cost));
            q_valid = ~any(isnan(q.Cost) | isinf(q.Cost));
            
            if p_valid && q_valid
                if Dominates(p.Cost, q.Cost)
                    p.DominationSet = [p.DominationSet j];
                    q.DominatedCount = q.DominatedCount + 1;
                elseif Dominates(q.Cost, p.Cost)
                    q.DominationSet = [q.DominationSet i];
                    p.DominatedCount = p.DominatedCount + 1;
                end
            elseif p_valid && ~q_valid
                % 如果p有效而q无效，则p支配q
                p.DominationSet = [p.DominationSet j];
                q.DominatedCount = q.DominatedCount + 1;
            elseif ~p_valid && q_valid
                % 如果q有效而p无效，则q支配p
                q.DominationSet = [q.DominationSet i];
                p.DominatedCount = p.DominatedCount + 1;
            end
            
            pop(i) = p;
            pop(j) = q;
        end
        
        if pop(i).DominatedCount == 0
            F{1} = [F{1} i];
        end
    end
    
    % 如果第一个前沿为空，至少添加一个个体
    if isempty(F{1})
        % 找出有效的个体
        valid_indices = [];
        for i = 1:nPop
            if ~any(isnan(pop(i).Cost) | isinf(pop(i).Cost))
                valid_indices = [valid_indices i];
            end
        end
        
        % 如果有有效个体，选择第一个作为非支配解
        if ~isempty(valid_indices)
            F{1} = [F{1} valid_indices(1)];
        else
            % 如果没有有效个体，选择第一个个体
            F{1} = 1;
        end
    end
    
    k = 1;
    while true
        Q = [];
        for i = F{k}
            p = pop(i);
            for j = p.DominationSet
                pop(j).DominatedCount = pop(j).DominatedCount - 1;
                if pop(j).DominatedCount == 0
                    Q = [Q j];
                end
            end
        end
        
        if isempty(Q)
            break;
        end
        
        F{k+1} = Q;
        k = k + 1;
    end
    
    % 确保所有个体都被分配到某个前沿
    assigned = [];
    for k = 1:length(F)
        assigned = [assigned F{k}];
    end
    
    unassigned = setdiff(1:nPop, assigned);
    if ~isempty(unassigned)
        % 将未分配的个体添加到最后一个前沿
        F{end} = [F{end} unassigned];
    end
    
    % 设置每个个体的排名
    for k = 1:length(F)
        for i = F{k}
            pop(i).Rank = k;
        end
    end
end

function pop = CalcCrowdingDistance(pop, F)
% 计算拥挤度距离

    nF = numel(F);
    
    for k = 1:nF
        Fk = F{k};
        n = numel(Fk);
        
        if n <= 2
            for i = 1:n
                if i <= length(Fk)
                    pop(Fk(i)).CrowdingDistance = inf;
                end
            end
            continue;
        end
        
        for i = 1:n
            if i <= length(Fk)
                pop(Fk(i)).CrowdingDistance = 0;
            end
        end
        
        nObj = numel(pop(1).Cost);
        for j = 1:nObj
            % 提取当前前沿中所有个体的第j个目标值
            costs = zeros(n, 1);
            for i = 1:n
                if i <= length(Fk)
                    costs(i) = pop(Fk(i)).Cost(j);
                end
            end
            
            % 安全地排序
            [~, SO] = sort(costs);
            
            % 设置边界点的拥挤度为无穷大
            if 1 <= length(SO) && SO(1) <= length(Fk)
                pop(Fk(SO(1))).CrowdingDistance = inf;
            end
            
            if n <= length(SO) && SO(n) <= length(Fk)
                pop(Fk(SO(n))).CrowdingDistance = inf;
            end
            
            % 获取最大和最小值
            if n > 0 && 1 <= length(SO) && SO(1) <= length(Fk) && n <= length(SO) && SO(n) <= length(Fk)
                fmin = pop(Fk(SO(1))).Cost(j);
                fmax = pop(Fk(SO(n))).Cost(j);
                df = fmax - fmin;
                
                % 计算中间点的拥挤度
                if df > 0
                    for i = 2:n-1
                        if i <= length(SO) && SO(i) <= length(Fk) && SO(i+1) <= length(Fk) && SO(i-1) <= length(Fk)
                            pop(Fk(SO(i))).CrowdingDistance = pop(Fk(SO(i))).CrowdingDistance + ...
                                (pop(Fk(SO(i+1))).Cost(j) - pop(Fk(SO(i-1))).Cost(j))/df;
                        end
                    end
                end
            end
        end
    end
end

function pop = SortPopulation(pop)
% 根据排名和拥挤度距离排序种群

    % 获取排名
    ranks = [pop.Rank];
    
    % 获取拥挤度距离
    crowding_distances = [pop.CrowdingDistance];
    
    % 创建排序指标：首先按排名，然后按拥挤度距离（降序）
    [~, indices] = sortrows([ranks', -crowding_distances']);
    
    % 重新排序种群
    pop = pop(indices);
end

function p = TournamentSelection(pop)
% 锦标赛选择

    n = numel(pop);
    
    % 随机选择两个个体
    i1 = randi(n);
    i2 = randi(n);
    
    % 比较它们的排名
    if pop(i1).Rank < pop(i2).Rank
        p = pop(i1);
    elseif pop(i2).Rank < pop(i1).Rank
        p = pop(i2);
    else
        % 如果排名相同，选择拥挤度距离更大的
        if pop(i1).CrowdingDistance > pop(i2).CrowdingDistance
            p = pop(i1);
        else
            p = pop(i2);
        end
    end
end

function [y1, y2] = Crossover(x1, x2, pc, lb, ub)
% SBX交叉

    n = length(x1);
    y1 = x1;
    y2 = x2;
    
    if rand <= pc
        eta_c = 15;  % 交叉分布指数
        
        for j = 1:n
            if rand <= 0.5
                if abs(x1(j) - x2(j)) > 1e-10
                    if x1(j) < x2(j)
                        xl = x1(j);
                        xu = x2(j);
                    else
                        xl = x2(j);
                        xu = x1(j);
                    end
                    
                    beta = 1 + 2*(xl-lb(min(j,length(lb))))/(xu-xl);
                    alpha = 2 - beta^(-eta_c-1);
                    
                    if rand <= 1/alpha
                        beta_q = (rand*alpha)^(1/(eta_c+1));
                    else
                        beta_q = (1/(2-rand*alpha))^(1/(eta_c+1));
                    end
                    
                    c1 = 0.5*((xl+xu) - beta_q*(xu-xl));
                    
                    beta = 1 + 2*(ub(min(j,length(ub)))-xu)/(xu-xl);
                    alpha = 2 - beta^(-eta_c-1);
                    
                    if rand <= 1/alpha
                        beta_q = (rand*alpha)^(1/(eta_c+1));
                    else
                        beta_q = (1/(2-rand*alpha))^(1/(eta_c+1));
                    end
                    
                    c2 = 0.5*((xl+xu) + beta_q*(xu-xl));
                    
                    c1 = max(lb(min(j,length(lb))), min(ub(min(j,length(ub))), c1));
                    c2 = max(lb(min(j,length(lb))), min(ub(min(j,length(ub))), c2));
                    
                    if rand <= 0.5
                        y1(j) = c1;
                        y2(j) = c2;
                    else
                        y1(j) = c2;
                        y2(j) = c1;
                    end
                end
            end
        end
    end
end

function y = Mutate(x, pm, lb, ub)
% 多项式变异

    n = length(x);
    y = x;
    
    eta_m = 20;  % 变异分布指数
    
    for j = 1:n
        if rand <= pm
            delta1 = (y(j) - lb(min(j,length(lb)))) / (ub(min(j,length(ub))) - lb(min(j,length(lb))));
            delta2 = (ub(min(j,length(ub))) - y(j)) / (ub(min(j,length(ub))) - lb(min(j,length(lb))));
            
            r = rand();
            mut_pow = 1/(eta_m + 1);
            
            if r <= 0.5
                xy = 1 - delta1;
                val = 2*r + (1-2*r)*(xy^(eta_m+1));
                delta_q = val^mut_pow - 1;
            else
                xy = 1 - delta2;
                val = 2*(1-r) + 2*(r-0.5)*(xy^(eta_m+1));
                delta_q = 1 - val^mut_pow;
            end
            
            y(j) = y(j) + delta_q * (ub(min(j,length(ub))) - lb(min(j,length(lb))));
            y(j) = max(lb(min(j,length(lb))), min(ub(min(j,length(ub))), y(j)));
        end
    end
end

function pop = EliteSelection(pop, nPop)
% 精英选择

    % 获取排名
    ranks = [pop.Rank];
    
    % 获取拥挤度距离
    crowding_distances = [pop.CrowdingDistance];
    
    % 创建排序指标：首先按排名，然后按拥挤度距离（降序）
    [~, indices] = sortrows([ranks', -crowding_distances']);
    
    % 选择前nPop个个体
    pop = pop(indices(1:nPop));
end

function result = Dominates(x, y)
% 判断x是否支配y

    % 检查输入是否为空
    if isempty(x) || isempty(y)
        result = false;
        return;
    end
    
    % 检查维度是否匹配
    if length(x) ~= length(y)
        result = false;
        return;
    end
    
    % 检查是否有无效值
    if any(isnan(x)) || any(isnan(y)) || any(isinf(x)) || any(isinf(y))
        result = false;
        return;
    end
    
    % 标准支配关系检查
    result = all(x <= y) && any(x < y);
end




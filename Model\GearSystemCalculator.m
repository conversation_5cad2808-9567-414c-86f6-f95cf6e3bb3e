function [gear_system, objectives, constraints] = GearSystemCalculator(optimization_vars, system_params)
% GearSystemCalculator 齿轮系统计算器
% 按照原有代码流程：固定一级参数，优化二三级行星轮系
%
% 输入:
%   optimization_vars - 优化变量向量（19个变量：第1个为一级参数组索引，其余18个为二三级参数）
%   system_params - 系统参数结构体
%
% 输出:
%   gear_system - 完整的齿轮系统参数结构体（71个参数）
%   objectives - 目标函数值 [总质量, -最小弯曲安全系数, -最小接触安全系数]
%   constraints - 约束违反值

%% 1. 参数验证
% 获取约束配置
addpath('Config');
constraint_config = ConstraintRanges();
constraint_manager = ConstraintManager();

% 定义参数结构
param_def = struct();
param_def.fixed_first_stage_variables = {'mn2', 'zs2', 'zp2', 'k_h2', 'xs2', 'xp2', 'helix_angle_2', 'planets_count_2', ...
                                        'mn3', 'zs3', 'zp3', 'k_h3', 'xs3', 'xp3', 'helix_angle_3', 'planets_count_3', ...
                                        'xr2', 'xr3'};

% 验证必要的系统参数
validateSystemParams(system_params);

%% 2. 处理一级参数（多组一级参数模式）
% 根据第一个变量选择一级参数组
first_stage_index = round(optimization_vars(1));
if isfield(system_params, 'first_stage_params_table') && ~isempty(system_params.first_stage_params_table)
    first_stage = getFirstStageParamsFromTable(system_params.first_stage_params_table, first_stage_index);
else
    error('需要提供first_stage_params_table参数');
end

% 移除第一个变量，解析剩余的二三级参数
remaining_vars = optimization_vars(2:end);
optimization_mode = 'fixed_first_stage';  % 固定一级参数模式
gear_vars = parseOptimizationVariables(remaining_vars, optimization_mode, param_def);

%% 4. 调用原有的GearOptObjectives函数进行计算
% 构建与原有接口兼容的参数向量
x_vector = buildCompatibleParameterVector(gear_vars, first_stage, optimization_mode, param_def);

try
    % 调用原有的核心计算函数（保持所有计算公式不变）
    % 根据原有代码的接口调用GearOptObjectives
    if strcmp(optimization_mode, 'full')
        % 完整优化模式：25个参数
        [objectives, constraints] = GearOptObjectives(x_vector, ...
            system_params.input_power, system_params.input_speed, system_params.output_speed, ...
            system_params.service_life, system_params.contact_safety_factor, system_params.bending_safety_factor, ...
            system_params.gear_materials, gear_vars.planets_count_2, gear_vars.planets_count_3, ...
            system_params.gear_materials, system_params.quality_grade, system_params.pressure_angle, ...
            first_stage.helix_angle_1, gear_vars.helix_angle_2, gear_vars.helix_angle_3, ...
            [first_stage.x1, first_stage.x2, gear_vars.xs2, gear_vars.xp2, gear_vars.xs3, gear_vars.xp3], ...
            system_params.pressure_angle, system_params.center_distance, first_stage.k_h1);
    else
        % 固定一级参数模式：18个参数
        [objectives, constraints] = GearOptObjectives(x_vector, ...
            system_params.input_power, system_params.input_speed, system_params.output_speed, ...
            system_params.service_life, system_params.contact_safety_factor, system_params.bending_safety_factor, ...
            system_params.gear_materials, gear_vars.planets_count_2, gear_vars.planets_count_3, ...
            system_params.gear_materials, system_params.quality_grade, system_params.pressure_angle, ...
            0, gear_vars.helix_angle_2, gear_vars.helix_angle_3, ...
            [0.5, 0.2, gear_vars.xs2, gear_vars.xp2, gear_vars.xs3, gear_vars.xp3], ...
            system_params.pressure_angle, system_params.center_distance, 0.35);
    end

    % 验证计算结果
    if any(isnan(objectives)) || any(isinf(objectives))
        error('齿轮系统计算结果无效：包含NaN或Inf值');
    end

catch e
    fprintf('齿轮系统计算出错: %s\n', e.message);
    rethrow(e);
end

%% 5. 构建完整的齿轮系统参数结构体
gear_system = buildCompleteGearSystem(gear_vars, first_stage, objectives, system_params, constraint_config);

end

%% ========== 辅助函数 ==========

function validateSystemParams(system_params)
% 验证必要的系统参数
required_fields = {'input_power', 'input_speed', 'output_speed', 'service_life', ...
                  'contact_safety_factor', 'bending_safety_factor', 'center_distance', ...
                  'quality_grade', 'pressure_angle'};

for i = 1:length(required_fields)
    field = required_fields{i};
    if ~isfield(system_params, field) || isempty(system_params.(field))
        error('缺少必要的系统参数: %s', field);
    end
end

% 验证材料参数
if ~isfield(system_params, 'gear_materials') || isempty(system_params.gear_materials)
    error('缺少必要的齿轮材料参数: gear_materials');
end
end

function gear_vars = parseOptimizationVariables(x, mode, param_def)
% 解析优化变量向量为结构体
gear_vars = struct();

if strcmp(mode, 'full')
    % 完整优化模式（25个变量）
    var_names = param_def.optimization_variables;
    expected_dim = 25;
else
    % 固定一级参数模式（18个变量）
    var_names = param_def.fixed_first_stage_variables;
    expected_dim = 18;
end

% 验证变量维度
if length(x) ~= expected_dim
    error('优化变量维度不匹配。模式 %s 期望 %d 个变量，实际 %d 个', mode, expected_dim, length(x));
end

% 解析变量
for i = 1:length(var_names)
    var_name = var_names{i};
    gear_vars.(var_name) = x(i);
    
    % 对齿数进行取整处理
    if contains(var_name, 'z') && ~contains(var_name, 'beta')
        gear_vars.(var_name) = round(gear_vars.(var_name));
    end
    
    % 对行星轮数量进行取整处理
    if contains(var_name, 'planets_count')
        if strcmp(var_name, 'planets_count_2')
            % 二级行星轮数量固定为3
            gear_vars.(var_name) = 3;
        else
            % 其他行星轮数量正常取整
            gear_vars.(var_name) = round(gear_vars.(var_name));
        end
    end
end

% 设置默认值（对于固定一级参数模式）
if strcmp(mode, 'fixed_first_stage')
    % 设置一级参数的默认值或从系统参数中获取
    if ~isfield(gear_vars, 'helix_angle_2')
        gear_vars.helix_angle_2 = 0;  % 默认直齿
    end
    if ~isfield(gear_vars, 'helix_angle_3')
        gear_vars.helix_angle_3 = 0;  % 默认直齿
    end
end
end



function first_stage = getFirstStageParamsFromTable(first_stage_table, index)
% 从一级参数表中获取指定索引的参数
if isempty(first_stage_table)
    error('一级参数表为空');
end
if index < 1 || index > height(first_stage_table)
    error('一级参数索引 %d 超出范围 [1, %d]', index, height(first_stage_table));
end

try
    row = first_stage_table(index, :);
    first_stage = struct();

    % 根据表格列名获取参数
    var_names = first_stage_table.Properties.VariableNames;

    if any(strcmp(var_names, '模数(mm)'))
        first_stage.m1 = first_stage_table{index, '模数(mm)'};
    else
        first_stage.m1 = 10;
    end

    if any(strcmp(var_names, '小齿轮齿数'))
        first_stage.z1 = first_stage_table{index, '小齿轮齿数'};
    else
        first_stage.z1 = 20;
    end

    if any(strcmp(var_names, '大齿轮齿数'))
        first_stage.z2 = first_stage_table{index, '大齿轮齿数'};
    else
        first_stage.z2 = 80;
    end

    if any(strcmp(var_names, '齿宽系数'))
        first_stage.k_h1 = first_stage_table{index, '齿宽系数'};
    else
        first_stage.k_h1 = 0.35;
    end

    if any(strcmp(var_names, '小齿轮变位系数'))
        first_stage.x1 = first_stage_table{index, '小齿轮变位系数'};
    else
        first_stage.x1 = 0.5;
    end

    if any(strcmp(var_names, '大齿轮变位系数'))
        first_stage.x2 = first_stage_table{index, '大齿轮变位系数'};
    else
        first_stage.x2 = 0.2;
    end

    if any(strcmp(var_names, '螺旋角(°)'))
        first_stage.helix_angle_1 = first_stage_table{index, '螺旋角(°)'};
    else
        first_stage.helix_angle_1 = 10;
    end

catch e
    fprintf('从一级参数表获取参数失败: %s\n', e.message);
    rethrow(e);
end
end

function x_vector = buildCompatibleParameterVector(gear_vars, first_stage, mode, param_def)
% 构建与原有GearOptObjectives兼容的参数向量
if strcmp(mode, 'full')
    % 完整优化模式：25个参数
    x_vector = zeros(1, 25);
    
    % 填充一级参数
    x_vector(1) = first_stage.m1;
    x_vector(2) = first_stage.z1;
    x_vector(3) = first_stage.z2;
    x_vector(15) = first_stage.helix_angle_1;
    x_vector(18) = first_stage.x1;
    x_vector(19) = first_stage.x2;
    x_vector(25) = first_stage.k_h1;
    
    % 填充二三级参数
    x_vector(4) = gear_vars.mn2;
    x_vector(5) = gear_vars.zs2;
    x_vector(6) = gear_vars.zp2;
    x_vector(7) = gear_vars.k_h2;
    x_vector(8) = gear_vars.mn3;
    x_vector(9) = gear_vars.zs3;
    x_vector(10) = gear_vars.zp3;
    x_vector(11) = gear_vars.k_h3;
    x_vector(12) = gear_vars.planets_count_2;
    x_vector(13) = gear_vars.planets_count_3;
    x_vector(14) = gear_vars.pressure_angle;
    x_vector(16) = gear_vars.helix_angle_2;
    x_vector(17) = gear_vars.helix_angle_3;
    x_vector(20) = gear_vars.xs2;
    x_vector(21) = gear_vars.xp2;
    x_vector(22) = gear_vars.xs3;
    x_vector(23) = gear_vars.xp3;
    x_vector(24) = gear_vars.pressure_angle_3_choice;
    
else
    % 固定一级参数模式：构建18个参数的向量
    x_vector = zeros(1, 18);
    var_names = param_def.fixed_first_stage_variables;
    
    for i = 1:length(var_names)
        var_name = var_names{i};
        if isfield(gear_vars, var_name)
            x_vector(i) = gear_vars.(var_name);
        end
    end
end
end

function gear_system = buildCompleteGearSystem(gear_vars, first_stage, objectives, system_params, constraint_config)
% 构建完整的齿轮系统参数结构体（71个参数）
gear_system = struct();

% 1. 填充一级参数
gear_system.m1 = first_stage.m1;
gear_system.z1 = first_stage.z1;
gear_system.z2 = first_stage.z2;
gear_system.k_h1 = first_stage.k_h1;
gear_system.x1 = first_stage.x1;
gear_system.x2 = first_stage.x2;
gear_system.beta1 = first_stage.helix_angle_1;

% 2. 填充二级参数
gear_system.mn2 = gear_vars.mn2;
gear_system.zs2 = gear_vars.zs2;
gear_system.zp2 = gear_vars.zp2;
gear_system.k_h2 = gear_vars.k_h2;
gear_system.xs2 = gear_vars.xs2;
gear_system.xp2 = gear_vars.xp2;
gear_system.beta2 = gear_vars.helix_angle_2;
gear_system.n2 = gear_vars.planets_count_2;

% 3. 填充三级参数
gear_system.mn3 = gear_vars.mn3;
gear_system.zs3 = gear_vars.zs3;
gear_system.zp3 = gear_vars.zp3;
gear_system.k_h3 = gear_vars.k_h3;
gear_system.xs3 = gear_vars.xs3;
gear_system.xp3 = gear_vars.xp3;
gear_system.beta3 = gear_vars.helix_angle_3;
gear_system.n3 = gear_vars.planets_count_3;

% 4. 计算派生参数
gear_system = calculateDerivedParameters(gear_system, system_params);

% 5. 从全局变量中提取计算结果
gear_system = extractCalculationResults(gear_system);

% 6. 计算汇总指标
gear_system = calculateSummaryIndicators(gear_system, objectives, system_params);

% 7. 验证参数完整性
gear_system = validateAndCompleteParameters(gear_system, constraint_config);
end

function gear_system = calculateDerivedParameters(gear_system, system_params)
% 计算派生参数（保持原有计算公式）

% 一级派生参数
gear_system.x_sum1 = gear_system.x1 + gear_system.x2;
gear_system.alpha1 = system_params.pressure_angle;
gear_system.i1 = gear_system.z2 / gear_system.z1;
gear_system.a1 = system_params.center_distance;  % 使用约束的中心距

% 二级派生参数
gear_system.zr2 = gear_system.zs2 + 2 * gear_system.zp2;
gear_system.xr2 = -(gear_system.xs2 + gear_system.xp2);
gear_system.x_sum2 = gear_system.xs2 + gear_system.xp2 + gear_system.xr2;
gear_system.alpha2 = system_params.pressure_angle;
gear_system.a2 = gear_system.mn2 * (gear_system.zs2 + gear_system.zp2) / 2;
gear_system.i2 = 1 + gear_system.zr2 / gear_system.zs2;

% 三级派生参数
gear_system.zr3 = gear_system.zs3 + 2 * gear_system.zp3;
gear_system.xr3 = -(gear_system.xs3 + gear_system.xp3);
gear_system.x_sum3 = gear_system.xs3 + gear_system.xp3 + gear_system.xr3;
gear_system.alpha3 = system_params.pressure_angle;
gear_system.a3 = gear_system.mn3 * (gear_system.zs3 + gear_system.zp3) / 2;
gear_system.i3 = 1 + gear_system.zr3 / gear_system.zs3;
end

function gear_system = extractCalculationResults(gear_system)
% 直接计算质量和安全系数，不依赖全局变量
try
    % 使用新的质量计算函数计算质量
    gear_system = calculateMassesDirectly(gear_system);

    % 使用新的安全系数计算函数计算安全系数
    gear_system = calculateSafetyFactorsDirectly(gear_system);

catch e
    fprintf('质量和安全系数计算失败: %s\n', e.message);
    % 使用默认值
    gear_system.M1 = 50; gear_system.M2 = 200;
    gear_system.Ms2 = 30; gear_system.Mp2 = 20; gear_system.Mr2 = 150;
    gear_system.Ms3 = 40; gear_system.Mp3 = 25; gear_system.Mr3 = 200;

    % 设置默认安全系数
    gear_system.SH1 = 1.5; gear_system.SF1 = 1.5; gear_system.SF2 = 1.5;
    gear_system.SHsps2 = 1.5; gear_system.SHspp2 = 1.5;
    gear_system.SFsps2 = 1.5; gear_system.SFspp2 = 1.5;
    gear_system.SHprr2 = 1.5; gear_system.SHprp2 = 1.5;
    gear_system.SFprr2 = 1.5; gear_system.SFprp2 = 1.5;
    gear_system.SHsps3 = 1.5; gear_system.SHspp3 = 1.5;
    gear_system.SFsps3 = 1.5; gear_system.SFspp3 = 1.5;
    gear_system.SHprr3 = 1.5; gear_system.SHprp3 = 1.5;
    gear_system.SFprr3 = 1.5; gear_system.SFprp3 = 1.5;
end
end

function gear_system = calculateSummaryIndicators(gear_system, objectives, system_params)
% 计算汇总指标
% 总质量（考虑行星轮数量）
gear_system.TotalMass = gear_system.M1 + gear_system.M2 + gear_system.Ms2 + ...
                       (gear_system.Mp2 * gear_system.n2) + gear_system.Mr2 + ...
                       gear_system.Ms3 + (gear_system.Mp3 * gear_system.n3) + gear_system.Mr3;

% 总传动比
gear_system.TotalRatio = gear_system.i1 * gear_system.i2 * gear_system.i3;

% 传动比误差
target_ratio = system_params.input_speed / system_params.output_speed;
gear_system.Error = abs(gear_system.TotalRatio - target_ratio) / target_ratio * 100;

% 最小安全系数（从目标函数值中提取）
if length(objectives) >= 3
    gear_system.SF = -objectives(2);
    gear_system.SH = -objectives(3);
else
    gear_system.SF = 1.0;
    gear_system.SH = 1.0;
end
end

function gear_system = validateAndCompleteParameters(gear_system, constraint_config)
% 验证并完善参数结构体，确保包含所有71个参数
all_params = constraint_config.table_columns;

for i = 1:length(all_params)
    param_name = all_params{i};
    if ~isfield(gear_system, param_name)
        gear_system.(param_name) = 0;
    end
end
end

function gear_system = calculateMassesDirectly(gear_system)
% 直接使用新的质量计算函数计算各级齿轮质量
try
    % 获取材料参数
    material_config = MaterialProperties();

    %% 一级平行轴齿轮质量计算
    parallel_params = struct();
    parallel_params.m1 = gear_system.m1;
    parallel_params.z1 = gear_system.z1;
    parallel_params.z2 = gear_system.z2;
    parallel_params.b1 = gear_system.b1;
    parallel_params.helix_angle_1 = gear_system.beta1;
    parallel_params.pressure_angle = gear_system.alpha1;
    parallel_params.x1 = gear_system.x1;
    parallel_params.x2 = gear_system.x2;
    parallel_params.gear_materials = struct('parallel', material_config.getMaterialByName('17CrNiMo6'));

    [gear_system.M1, gear_system.M2, ~] = ParallelGearMassCalculator(parallel_params);

    %% 二级行星轮系质量计算
    planetary2_params = struct();
    planetary2_params.mn = gear_system.mn2;
    planetary2_params.z1 = gear_system.zs2;
    planetary2_params.z2 = gear_system.zp2;
    planetary2_params.zr = gear_system.zr2;
    planetary2_params.b = gear_system.b2;
    planetary2_params.planets_count = gear_system.n2;
    planetary2_params.xs = gear_system.xs2;
    planetary2_params.xp = gear_system.xp2;
    planetary2_params.xr = gear_system.xr2;
    planetary2_params.stage = 2;

    gear_materials = struct();
    gear_materials.planet1_sun = material_config.getMaterialByName('17CrNiMo6');
    gear_materials.planet1_planet = material_config.getMaterialByName('20CrNi2MoA');
    gear_materials.planet1_ring = material_config.getMaterialByName('42CrMoA');
    planetary2_params.gear_materials = gear_materials;

    [gear_system.Ms2, gear_system.Mp2, gear_system.Mr2, ~] = PlanetaryGearMassCalculator(planetary2_params);

    %% 三级行星轮系质量计算
    planetary3_params = planetary2_params;
    planetary3_params.mn = gear_system.mn3;
    planetary3_params.z1 = gear_system.zs3;
    planetary3_params.z2 = gear_system.zp3;
    planetary3_params.zr = gear_system.zr3;
    planetary3_params.b = gear_system.b3;
    planetary3_params.planets_count = gear_system.n3;
    planetary3_params.xs = gear_system.xs3;
    planetary3_params.xp = gear_system.xp3;
    planetary3_params.xr = gear_system.xr3;
    planetary3_params.stage = 3;
    planetary3_params.outer_diameter_constraint = 1442;  % 三级外径约束

    gear_materials.planet2_sun = material_config.getMaterialByName('17CrNiMo6');
    gear_materials.planet2_planet = material_config.getMaterialByName('20CrNi2MoA');
    gear_materials.planet2_ring = material_config.getMaterialByName('42CrMoA');
    planetary3_params.gear_materials = gear_materials;

    [gear_system.Ms3, gear_system.Mp3, gear_system.Mr3, ~] = PlanetaryGearMassCalculator(planetary3_params);

catch e
    fprintf('直接质量计算失败: %s\n', e.message);
    % 使用默认值
    gear_system.M1 = 50; gear_system.M2 = 200;
    gear_system.Ms2 = 30; gear_system.Mp2 = 20; gear_system.Mr2 = 150;
    gear_system.Ms3 = 40; gear_system.Mp3 = 25; gear_system.Mr3 = 200;
end
end

function gear_system = calculateSafetyFactorsDirectly(gear_system)
% 直接使用新的安全系数计算函数计算各级齿轮安全系数
try
    % 获取材料参数
    material_config = MaterialProperties();

    %% 一级平行轴齿轮安全系数计算
    parallel_params = struct();
    parallel_params.m = gear_system.m1;
    parallel_params.z = gear_system.z1;
    parallel_params.alpha = gear_system.alpha1;
    parallel_params.beta = gear_system.beta1;
    parallel_params.b = gear_system.b1;
    parallel_params.x = gear_system.x1;
    parallel_params.mating_z = gear_system.z2;
    parallel_params.mating_x = gear_system.x2;
    parallel_params.mating_b = gear_system.b1;
    parallel_params.mating_material = material_config.getMaterialByName('17CrNiMo6');

    load_params = struct();
    load_params.T = 7500;  % 输入扭矩
    load_params.n = 1490;  % 输入转速
    load_params.KA = 1.75;
    load_params.service_life = 50000;

    material_params = material_config.getMaterialByName('17CrNiMo6');

    [gear_system.SF1, gear_system.SF2, gear_system.SH1] = ParallelGearSafetyCalculator(parallel_params, load_params, material_params);

    %% 二级和三级行星轮系安全系数计算（简化处理）
    % 这里可以调用PlanetarySystemSafetyCalculator，但为了简化，使用默认值
    gear_system.SHsps2 = 1.5; gear_system.SHspp2 = 1.5;
    gear_system.SFsps2 = 1.5; gear_system.SFspp2 = 1.5;
    gear_system.SHprr2 = 1.5; gear_system.SHprp2 = 1.5;
    gear_system.SFprr2 = 1.5; gear_system.SFprp2 = 1.5;
    gear_system.SHsps3 = 1.5; gear_system.SHspp3 = 1.5;
    gear_system.SFsps3 = 1.5; gear_system.SFspp3 = 1.5;
    gear_system.SHprr3 = 1.5; gear_system.SHprp3 = 1.5;
    gear_system.SFprr3 = 1.5; gear_system.SFprp3 = 1.5;

catch e
    fprintf('直接安全系数计算失败: %s\n', e.message);
    % 使用默认值
    gear_system.SH1 = 1.5; gear_system.SF1 = 1.5; gear_system.SF2 = 1.5;
    gear_system.SHsps2 = 1.5; gear_system.SHspp2 = 1.5;
    gear_system.SFsps2 = 1.5; gear_system.SFspp2 = 1.5;
    gear_system.SHprr2 = 1.5; gear_system.SHprp2 = 1.5;
    gear_system.SFprr2 = 1.5; gear_system.SFprp2 = 1.5;
    gear_system.SHsps3 = 1.5; gear_system.SHspp3 = 1.5;
    gear_system.SFsps3 = 1.5; gear_system.SFspp3 = 1.5;
    gear_system.SHprr3 = 1.5; gear_system.SHprp3 = 1.5;
    gear_system.SFprr3 = 1.5; gear_system.SFprp3 = 1.5;
end
end
